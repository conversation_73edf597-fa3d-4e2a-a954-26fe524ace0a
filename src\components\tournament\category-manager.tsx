'use client';

import * as React from 'react';
import { Button } from '@/components/ui/button';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import {
  TournamentCategory,
  type TournamentCategoryData,
} from './tournament-category';
import { Plus, Trash2, Trophy } from 'lucide-react';
import { cn } from '@/lib/utils';

export interface CategoryManagerProps {
  categories: TournamentCategoryData[];
  onCategoriesChange: (categories: TournamentCategoryData[]) => void;
  tournamentName?: string;
  className?: string;
}

export function CategoryManager({
  categories,
  onCategoriesChange,
  tournamentName,
  className,
}: CategoryManagerProps) {
  const addCategory = () => {
    const newCategory: TournamentCategoryData = {
      id: `category-${Date.now()}`,
      name: '',
      prizes: [
        { place: 1, amount: '' },
        { place: 2, amount: '' },
        { place: 3, amount: '' },
      ],
      isDefault: false,
    };
    onCategoriesChange([...categories, newCategory]);
  };

  const updateCategory = (
    id: string,
    updatedCategory: TournamentCategoryData,
  ) => {
    onCategoriesChange(
      categories.map((cat) => (cat.id === id ? updatedCategory : cat)),
    );
  };

  const removeCategory = (id: string) => {
    onCategoriesChange(categories.filter((cat) => cat.id !== id));
  };

  return (
    <div className={cn('space-y-4', className)}>
      <div className="text-sm text-muted-foreground">
        <p>
          Set up different tournament categories with their own prize
          distributions. You can select from common categories or create custom
          ones.
        </p>
      </div>

      {categories.length === 0 && (
        <div className="text-center py-8 text-muted-foreground">
          <Trophy className="h-8 w-8 mx-auto mb-3 opacity-50" />
          <p className="text-sm font-medium">No categories added yet</p>
          <p className="text-xs mt-1">
            Add tournament categories to set up prizes for different player
            groups.
          </p>
        </div>
      )}

      {categories.length > 0 && (
        <Accordion type="multiple" className="w-full space-y-3">
          {categories.map((category, index) => (
            <AccordionItem
              key={category.id}
              value={category.id}
              className="border border-border rounded-lg bg-background shadow-sm"
            >
              <AccordionTrigger className="cursor-pointer px-4 py-4 hover:no-underline hover:bg-muted/50 rounded-lg [&[data-state=open]]:rounded-b-none [&[data-state=open]]:border-b [&[data-state=open]]:border-border items-center">
                <div className="flex items-center justify-between w-full">
                  <div className="flex items-center gap-3">
                    <Trophy className="h-4 w-4 text-muted-foreground" />
                    <span className="font-medium text-base">
                      {category.name ||
                        (category.isDefault
                          ? tournamentName || 'Default Category'
                          : `Category ${index + 1}`)}
                    </span>
                    {category.isDefault && (
                      <span className="text-xs bg-primary/10 text-primary px-2 py-1 rounded-full">
                        Default
                      </span>
                    )}
                  </div>
                  {categories.length > 1 && !category.isDefault && (
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation();
                        removeCategory(category.id);
                      }}
                      className="text-destructive/50 hover:text-destructive hover:bg-destructive/10 h-6 w-6 p-0 mr-1"
                    >
                      <Trash2 className="h-3 w-3" />
                    </Button>
                  )}
                </div>
              </AccordionTrigger>
              <AccordionContent className="px-4 pb-4">
                <TournamentCategory
                  category={category}
                  onCategoryChange={(updatedCategory) =>
                    updateCategory(category.id, updatedCategory)
                  }
                />
              </AccordionContent>
            </AccordionItem>
          ))}
        </Accordion>
      )}

      <div className="flex justify-end">
        <Button
          type="button"
          variant="outline"
          onClick={addCategory}
          className=""
        >
          <Plus className="h-4 w-4 mr-2" />
          Add Category
        </Button>
      </div>
    </div>
  );
}
